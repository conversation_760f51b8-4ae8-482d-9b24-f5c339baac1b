'use client';

import React, { forwardRef, ForwardedRef, useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import Link from 'next/link';
import { Bell, User, LogOut, Menu } from 'lucide-react';
import { useRouter } from 'next/navigation';
import useUserStore from '@/store/userStore';
import { deleteCookie } from 'cookies-next';

import RotatingText from '@/components/ui/RotationText';
import useNotifications from '@/hooks/useNotifications';
import NotificationPopover from '@/components/notifications/NotificationPopover';


const HeaderContainer = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1.5rem;
  background-color: transparent;
  border-bottom: 1px solid transparent;
  height: 80px;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 50;
  // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  /* Tablet */
  @media (max-width: 1023px) {
    height: 70px;
    padding: 0.625rem 1.25rem;
  }

  /* Mobile - Transform to bottom navigation */
  @media (max-width: 767px) {
    position: fixed;
    bottom: 0;
    top: auto;
    height: 80px;
    padding: 0.5rem 1rem;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: none;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }

  /* Small mobile */
  @media (max-width: 480px) {
    height: 70px;
    padding: 0.375rem 0.75rem;
  }
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
  column-gap: 1rem;

  /* Tablet */
  @media (max-width: 1023px) {
    column-gap: 0.75rem;
  }

  /* Mobile - Center the logo/brand */
  @media (max-width: 767px) {
    flex: 1;
    justify-content: center;
    column-gap: 0.5rem;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    column-gap: 0.25rem;
  }
`;

const Logo = styled.div`
  font-size: 2.25rem;
  font-weight: 700;
  color: #474747;

  /* Tablet */
  @media (max-width: 1023px) {
    font-size: 2rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 1.5rem;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    font-size: 1.25rem;
  }
`;

const SearchBar = styled.div`
  position: relative;
  display: flex;
  align-items: center;

  @media (max-width: 768px) {
    display: none;
  }
`;

const SearchInput = styled.input`
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  outline: none;
  width: 250px;
  font-size: 0.875rem;
  background-color: #f9f9f9;

  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
    background-color: #ffffff;
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 0.75rem;
  color: #6b7280;
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;

  /* Tablet */
  @media (max-width: 1023px) {
    gap: 0.75rem;
  }

  /* Mobile - Position on the right side of bottom nav */
  @media (max-width: 767px) {
    gap: 0.5rem;
    position: absolute;
    right: 1rem;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    gap: 0.375rem;
    right: 0.75rem;
  }
`;

const IconButton = styled.button`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.25));
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #98979c;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: 50px;
  height: 50px;

  &:hover {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.075), rgba(0, 0, 0, 0));
  }

  /* Tablet */
  @media (max-width: 1023px) {
    width: 45px;
    height: 45px;
  }

  /* Mobile */
  @media (max-width: 767px) {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 0.9);
    }
  }

  /* Small mobile */
  @media (max-width: 480px) {
    width: 40px;
    height: 40px;
  }
`;

const NotificationCountBadge = styled.div<{ $count: number; $show: boolean }>`
  position: absolute;
  top: -8px;
  right: -8px;
  min-width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 10px;
  display: ${props => (props.$show ? 'flex' : 'none')};
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  padding: 0 6px;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  z-index: 1;

  /* Subtle animation effects */
  animation: ${props => (props.$count > 0 ? 'pulse 2s infinite' : 'none')};
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.9;
    }
  }

  /* Bounce effect when count changes */
  &.count-updated {
    animation: bounce 0.6s ease-out;
  }

  @keyframes bounce {
    0% {
      transform: scale(1);
    }
    25% {
      transform: scale(1.15);
    }
    50% {
      transform: scale(0.95);
    }
    75% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }
`;

const UserAvatar = styled.div<{ $hasImage?: boolean }>`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: ${props =>
    props.$hasImage
      ? 'none'
      : 'linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.25))'};
  display: flex;
  align-items: center;
  justify-content: center;
  color: #98979c;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background-size: cover;
  background-position: center;
  overflow: hidden;

  /* Tablet */
  @media (max-width: 1023px) {
    width: 45px;
    height: 45px;
    font-size: 0.9rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    width: 44px;
    height: 44px;
    font-size: 0.875rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: ${props =>
      props.$hasImage
        ? 'none'
        : 'rgba(255, 255, 255, 0.8)'};
  }

  /* Small mobile */
  @media (max-width: 480px) {
    width: 40px;
    height: 40px;
    font-size: 0.8rem;
  }
`;

const PopoverMenu = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 50px;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: ${props => (props.$isOpen ? 'block' : 'none')};
  z-index: 99999;
  min-width: 220px;

  &::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 10px;
    transform: rotate(45deg);
    width: 12px;
    height: 12px;
    background-color: white;
    border-radius: 2px;
    box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.05);
  }
`;

const PopoverItem = styled.div`
  a,
  button {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: #98979c;
    width: 100%;
    text-align: left;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.95rem;

    &:hover {
      background: linear-gradient(135deg, rgba(0, 0, 0, 0.075), rgba(0, 0, 0, 0));
    }
  }
`;

const PopoverHeader = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  font-weight: 500;
  color: #333;
  font-size: 0.95rem;
  word-break: break-word;
`;

const PopoverIcon = styled.span`
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const UserProfileContainer = styled.div`
  position: relative;
`;

const MobileMenuButton = styled.button`
  display: none;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #474747;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: 44px;
  height: 44px;
  position: absolute;
  left: 1rem;

  &:hover {
    background: rgba(255, 255, 255, 0.9);
  }

  /* Mobile */
  @media (max-width: 767px) {
    display: flex;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    width: 40px;
    height: 40px;
    left: 0.75rem;
  }
`;

interface HeaderProps {
  className?: string;
  onMobileMenuToggle?: () => void;
}

const Header = forwardRef<HTMLElement, HeaderProps>(
  ({ className, onMobileMenuToggle }, ref: ForwardedRef<HTMLElement>) => {
    const [isPopoverOpen, setIsPopoverOpen] = useState(false);
    const [isNotificationPopoverOpen, setIsNotificationPopoverOpen] = useState(false);
    const [previousUnreadCount, setPreviousUnreadCount] = useState(0);
    const [countUpdated, setCountUpdated] = useState(false);
    const popoverRef = useRef<HTMLDivElement>(null);
    const avatarRef = useRef<HTMLDivElement>(null);
    const notificationBtnRef = useRef<HTMLButtonElement>(null);
    const notificationPopoverRef = useRef<HTMLDivElement>(null);
    const { userData: user, loading: isLoading, fetchUserData } = useUserStore();
    const { unreadCount, fetchNotifications } = useNotifications({ autoRefresh: true });
    const router = useRouter();

    const togglePopover = () => {
      setIsPopoverOpen(!isPopoverOpen);
      if (isNotificationPopoverOpen) setIsNotificationPopoverOpen(false);
    };

    const toggleNotificationPopover = () => {
      setIsNotificationPopoverOpen(!isNotificationPopoverOpen);
      if (isPopoverOpen) setIsPopoverOpen(false);
    };

    // Callback to refresh notification count when notifications are read in popover
    const handleNotificationRead = () => {
      fetchNotifications();
    };

    // Use the fetchUserData from the store
    useEffect(() => {
      fetchUserData();
    }, [fetchUserData]);

    // Handle unread count changes for animation
    useEffect(() => {
      if (unreadCount !== previousUnreadCount) {
        if (previousUnreadCount > 0 && unreadCount !== previousUnreadCount) {
          setCountUpdated(true);
          setTimeout(() => setCountUpdated(false), 600); // Match animation duration
        }
        setPreviousUnreadCount(unreadCount);
      }
    }, [unreadCount, previousUnreadCount]);

    // Listen for notification read events from other components
    useEffect(() => {
      const handleNotificationRead = () => {
        fetchNotifications();
      };

      window.addEventListener('notificationRead', handleNotificationRead);
      return () => {
        window.removeEventListener('notificationRead', handleNotificationRead);
      };
    }, [fetchNotifications]);

    // Close popovers when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        // Handle user profile popover
        if (
          popoverRef.current &&
          avatarRef.current &&
          !popoverRef.current.contains(event.target as Node) &&
          !avatarRef.current.contains(event.target as Node)
        ) {
          setIsPopoverOpen(false);
        }

        // Handle notification popover
        if (
          notificationPopoverRef.current &&
          notificationBtnRef.current &&
          !notificationPopoverRef.current.contains(event.target as Node) &&
          !notificationBtnRef.current.contains(event.target as Node)
        ) {
          setIsNotificationPopoverOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    // Handle logout
    const handleLogout = () => {
      // Clear the token using cookies-next
      deleteCookie('access_token');
      // Redirect to login
      router.push('/login');
    };

    // Get user initials or placeholder
    const getUserInitials = () => {
      if (isLoading) return '...';
      if (user?.firstName && user?.lastName) {
        return `${user.firstName[0]}${user.lastName[0]}`;
      }
      return 'U';
    };



    return (
      <HeaderContainer ref={ref} className={className}>
        <MobileMenuButton onClick={onMobileMenuToggle}>
          <Menu size={20} />
        </MobileMenuButton>
        <LeftSection>
          <Logo>
            <span style={{ color: '#474747' }}>เฮีย</span> ·{' '}
            <span style={{ color: '#f56042' }}>สั่ง</span> ·{' '}
            <span style={{ color: '#474747' }}>มา</span>
          </Logo>
          <RotatingText
            texts={['Task', 'Manangement', 'Reward', 'Productivity']}
            mainClassName="flex items-center text-gray-500 text-xs md:text-sm lg:text-lg font-bold px-2 md:px-3 lg:px-4 h-6 md:h-8 lg:h-10 rounded-md bg-[#474747] text-white"
            staggerFrom={'last'}
            staggerDuration={0.025}
            splitLevelClassName="overflow-hidden"
            transition={{ type: 'spring', damping: 30, stiffness: 400 }}
            rotationInterval={2000}
          />
        </LeftSection>
        <RightSection>
          <div style={{ position: 'relative' }}>
            <IconButton ref={notificationBtnRef} onClick={toggleNotificationPopover}>
              <Bell size={24} />
              <NotificationCountBadge
                $count={unreadCount}
                $show={unreadCount > 0}
                className={countUpdated ? 'count-updated' : ''}
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </NotificationCountBadge>
            </IconButton>
            {isNotificationPopoverOpen && (
              <NotificationPopover
                isOpen={true}
                onClose={() => setIsNotificationPopoverOpen(false)}
                onNotificationRead={handleNotificationRead}
                ref={notificationPopoverRef}
              />
            )}
          </div>
          <UserProfileContainer>
            <UserAvatar
              onClick={togglePopover}
              ref={avatarRef}
              $hasImage={!!user?.imageUrl}
              style={user?.imageUrl ? { backgroundImage: `url(${user.imageUrl})` } : {}}
            >
              {!user?.imageUrl && getUserInitials()}
            </UserAvatar>

            <PopoverMenu $isOpen={isPopoverOpen} ref={popoverRef}>
              <PopoverHeader>
                {user ? `${user.firstName} ${user.lastName}` : 'User'}
                <div style={{ fontSize: '0.8rem', color: '#666', marginTop: '2px' }}>
                  {user?.role?.name || 'Guest'}
                </div>
                {user?.email && (
                  <div
                    style={{
                      fontSize: '0.8rem',
                      color: '#666',
                      marginTop: '2px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {user.email}
                  </div>
                )}
              </PopoverHeader>
              <PopoverItem>
                <Link href="/settings">
                  <PopoverIcon>
                    <User size={18} />
                  </PopoverIcon>
                  Profile
                </Link>
              </PopoverItem>
              <PopoverItem>
                <Link href="/notification">
                  <PopoverIcon>
                    <Bell size={18} />
                  </PopoverIcon>
                  Notifications
                </Link>
              </PopoverItem>
              <PopoverItem>
                <button onClick={handleLogout}>
                  <PopoverIcon>
                    <LogOut size={18} />
                  </PopoverIcon>
                  Logout
                </button>
              </PopoverItem>
            </PopoverMenu>
          </UserProfileContainer>
        </RightSection>
      </HeaderContainer>
    );
  }
);

Header.displayName = 'Header';

export default Header;
